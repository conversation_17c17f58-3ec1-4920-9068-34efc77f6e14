<?php

namespace App\Http\Resources\Product;

use App\Models\Product\PriceUnit;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming ProductVariation models into API responses.
 */
class ProductVariationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Basic variation data (id, sku, price, current_quantity)
     * - All attributes directly in the variation object (e.g., color, size)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        // Start with the basic variation data
        $data = [
            'id' => (string) $this->id,
            'sku' => $this->sku,
            'price' => $this->price,
            'sale_price' => $this->sale_price,
            'price_unit' => $this->whenLoaded('priceUnit', fn() => $this->priceUnit?->name) ?? PriceUnit::getDefault()?->name,
            'current_quantity' => $this->stock,
            'in_stock' => $this->stock > 0,
            'attributes' => $this->getAttributes()
        ];
        foreach ($this->attributes as $attribute) {
            $attributeType = $attribute->attribute_type;
            $attributeValue = $attribute->attribute_value;

            // For all attributes, just include the value
            $data[$attributeType] = $attributeValue;
        }
        return $data;
    }

    private function getAttributes(): array
    {
        $data = [];
        foreach ($this->attributes as $attribute) {
            $attributeType = $attribute->attribute_type;
            $attributeTitle = $attribute->attribute_title;
            $attributeValue = $attribute->attribute_value;
            $extra = $attribute->extra_data;
            // For all attributes, just include the value
            $data[] = [
                "title" => $attributeTitle,
                "type" => $attributeType,
                "value" => $attributeValue,
                "extra" => $extra,

            ];
        }
        return $data;
    }
}
