<?php

namespace App\Providers;

use Dedoc\Scramble\Scramble;
use Dedoc\Scramble\Support\Generator\OpenApi;
use Dedoc\Scramble\Support\Generator\SecurityScheme;
use Illuminate\Support\ServiceProvider;
use App\Services\Payment\Contracts\PaymentContract;
use App\Services\Payment\Factories\PaymentFactory;
use Dedoc\Scramble\Support\Generator\SecurityRequirement;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(PaymentContract::class, fn() => PaymentFactory::create());

    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Scramble::configure()
            ->withDocumentTransformers(function (OpenApi $openApi) {
                /* 1️⃣  Declare both schemes in the Components section */
                $openApi->components->securitySchemes['bearerAuth'] =
                    SecurityScheme::http('bearer');                     // Authorization: Bearer …
    
                $openApi->components->securitySchemes['appKey'] =
                    SecurityScheme::apiKey('header', 'X-Application-Token');

                $openApi->security[] = new SecurityRequirement([
                    'bearerAuth' => [],
                    'appKey' => [],
                ]);

                $openApi->components->securitySchemes['appKey']->description =
                    'Application key header. Example: X-Application-Token: YOUR_APP_KEY';
            });


    }
}
