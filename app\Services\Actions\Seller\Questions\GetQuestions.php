<?php

namespace App\Services\Actions\Seller\Questions;

use App\Enums\Product\StatusEnum;
use App\Enums\Question\QuestionFilterEnum;
use App\Models\UserInteraction\Question;
use App\Traits\Pagination;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;

class GetQuestions
{
    use Pagination;

    /**
     * Get questions present for a seller
     * @param array $data
     * @return LengthAwarePaginator
     */
    public function handle(array $data): LengthAwarePaginator
    {
        $filter = isset($data['filter']) ? QuestionFilterEnum::from($data['filter']) : null;
        $status = isset($data['status']) ? StatusEnum::fromString($data['status']) : null;

        /** @var Builder $query */
        $query = Question::query()
            ->with('answers')
            ->search($data['search'] ?? null)
            ->status($status);

        // apply answered / unanswered filter only if requested
        if ($filter === QuestionFilterEnum::ANSWERED) {
            $query->answered();
        } elseif ($filter === QuestionFilterEnum::UNANSWERED) {
            $query->unanswered();
        }

        return $this->applyPagination($query, $data);
    }
}
