<?php

namespace App\Http\Resources\Shopping;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Request;
use App\Http\Resources\Shopping\CartItemResource;
use App\Models\Product\PriceUnit;

/**
 * Cart Resource
 */
class CartResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $items = $this['model']->relationLoaded('items') ? $this['model']->items : null;



        return [
            // User ID of the cart owner
            'user_id' => $this['model']->user_id,

            // Transform each cart item using CartItemResource
            'items' => CartItemResource::collection($items),

            // Cart pricing information
            'subtotal' => $this['subtotal'],
            'total_discount' => $this['total_discount'],
            'total' => $this['total'],
            'price_unit' => $this['price_unit'] ?? PriceUnit::getDefault()?->name,
        ];
    }
}

