<?php

namespace App\Http\Controllers\Api\V1\Public;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\CityResource;
use App\Models\City;
use App\Services\Actions\Cities\GetCities;

class CityController extends BaseController
{
    public function index(GetCities $action)
    {
        $provinces = $action->handle();

        return $this->sendResponse(CityResource::collection($provinces));
    }

    public function show(City $city, GetCities $action)
    {
        $provinces = $action->handle($city);

        return $this->sendResponse(CityResource::collection($provinces));
    }
}
