<?php

namespace App\Services\Actions\Seller\Answer;

use App\Models\UserInteraction\Question;

class AnswerQuestion
{

    /**
     * submit answer for a question
     * @param array $data
     * @return Question|null
     */
    public function handle(array $data)
    {
        $userId = auth()->id();
        $question = Question::with('answers')->where('uuid', $data['question_id'])->first();

        $question->answers()->create([
            'body' => $data['body'],
            'user_id' => $userId,
        ]);

        return $question;
    }
}
