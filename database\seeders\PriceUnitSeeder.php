<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product\PriceUnit;
use Illuminate\Support\Facades\DB;

/**
 * PriceUnit Seeder
 * 
 * Seeds the price_units table with common currency units.
 */
class PriceUnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $priceUnits = [
                [
                    'name' => 'تومان',
                    'symbol' => '﷼',
                    'code' => 'TMN',
                    'is_active' => true,
                    'is_default' => true,
                ],
                [
                    'name' => 'ریال',
                    'symbol' => '﷼',
                    'code' => 'IRR',
                    'is_active' => true,
                    'is_default' => false,
                ],
                [
                    'name' => 'دلار آمریکا',
                    'symbol' => '$',
                    'code' => 'USD',
                    'is_active' => true,
                    'is_default' => false,
                ],
                [
                    'name' => 'یورو',
                    'symbol' => '€',
                    'code' => 'EUR',
                    'is_active' => true,
                    'is_default' => false,
                ],
                [
                    'name' => 'درهم امارات',
                    'symbol' => 'د.إ',
                    'code' => 'AED',
                    'is_active' => true,
                    'is_default' => false,
                ],
            ];

            foreach ($priceUnits as $unit) {
                PriceUnit::firstOrCreate(
                    ['code' => $unit['code']],
                    $unit
                );
            }

            $this->command->info('Price units seeded successfully.');
        });
    }
}
