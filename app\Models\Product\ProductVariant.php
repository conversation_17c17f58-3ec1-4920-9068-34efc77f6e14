<?php

namespace App\Models\Product;

use App\Services\Actions\Notification\SendLowStockNotification;
use App\Traits\ProductVariant\ProductVariantRelationsTrait;
use App\Traits\ProductVariant\ProductVariantAttributesTrait;
use Illuminate\Database\Eloquent\Model;

class ProductVariant extends Model
{
    use ProductVariantRelationsTrait, ProductVariantAttributesTrait;


    protected $fillable = ['product_id', 'sku', 'price', 'sale_price', 'price_unit_id'];

    protected $casts = [
        'price' => 'float',
        'sale_price' => 'float',
    ];

    /**
     * Boot the model and register event listeners.
     */
    protected static function booted()
    {
        static::updated(function ($variant) {
            // Check if stock field was updated and is now below 3
            if ($variant->wasChanged('stock') && $variant->stock < 3 && $variant->stock >= 0) {
                // Use app() to resolve the action with its dependencies
                $lowStockAction = app(SendLowStockNotification::class);
                $lowStockAction->handle(['product_variant_id' => $variant->id]);
            }
        });
    }
}