<?php

namespace App\Services\Actions\Product;

use App\Exceptions\BusinessException;
use App\Models\Product\Attribute;
use App\Models\Product\Category;
use App\Models\Product\Product;
use App\Models\Product\ProductVariant;

class CreateProductVariant
{
    public function handle(array $data): ProductVariant
    {
        $product = Product::where('slug', $data["product_slug"])->with('category')->first();
        if (isset($data['attributes'])) {
            $attributes = $data['attributes'];
            //reverse sort cuz persian is rtl
            krsort($attributes);
            $values = array_column($attributes, 'value');
            // (array_values($attributes))
            $sku = implode('-', [$product->title, ...$values]);
        } else {
            $attributes = null;
            $sku = "{$product->title}-default";
        }

        $variant = null;

        try {
            \DB::transaction(function () use (&$variant, $product, $sku, $data, $attributes) {

                $variant = $this->createVariant($product, $sku, $data);
                $category = $product->category;

                $this->createVariantAttributes($variant, $category, $attributes);
            });
        } catch (\Throwable $th) {
            throw new BusinessException(__("messages.product.variant_already_exists"));
        }

        return $variant;
    }

    private function createVariant(Product $product, string $sku, array $data): ProductVariant
    {
        return $product->variants()->create([
            'sku' => $sku,
            'price' => $data['price'],
            'sale_price' => $data['sale_price'] ?? null,
            'price_unit_id' => $data['price_unit_id'],
            'stock' => $data['stock'],
        ]);

    }

    private function createVariantAttributes(ProductVariant $variant, Category $category, ?array $attributes): void
    {

        if ($attributes)
            foreach ($attributes as $attribute) {

                //attrbiute that admin has declared
                $realAttribute = Attribute::where('category_id', $category->id)
                    ->where('title', $attribute['value'])
                    ->first();
                $variant->attributes()->create([
                    "attribute_title" => $attribute['title'],
                    "attribute_value" => $attribute['english_title'],
                    "attribute_type" => $attribute['value'],
                    "extra_data" => json_decode($realAttribute->value, 1)
                ]);

            }

    }
}
