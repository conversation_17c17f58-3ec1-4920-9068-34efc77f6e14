<?php

namespace App\Http\Controllers\Api\V1\Private\Seller\Answers;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Seller\AnswerQuestionRequest;
use App\Http\Requests\Seller\UpdateAnswerRequest;
use App\Http\Resources\UserInteraction\QuestionResource;
use App\Models\UserInteraction\Answer;
use App\Services\Actions\Seller\Answer\AnswerQuestion;
use App\Services\Actions\Seller\Answer\UpdateAnswer;

class AnswerController extends BaseController
{

    /**
     * submit answer for a question
     * @param \App\Http\Requests\Seller\AnswerQuestionRequest $request
     * @param \App\Services\Actions\Seller\Answer\AnswerQuestion $action
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(AnswerQuestionRequest $request, AnswerQuestion $action)
    {
        $question = $action->handle($request->validated());

        return $this->sendResponse(
            new QuestionResource($question),
            __('messages.question.found')
        );

    }
    /**
     * update answer for a question
     * @param \App\Http\Requests\Seller\UpdateAnswerRequest $request
     * @param \App\Services\Actions\Seller\Answer\UpdateAnswer $action
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateAnswerRequest $request, Answer $answer, UpdateAnswer $action)
    {
        $question = $action->handle([
            ...$request->validated(),
            'model' => $answer
        ]);

        return $this->sendResponse(
            new QuestionResource($question),
            __('messages.question.found')
        );

    }
}
