<?php

namespace App\Enums\User;

use App\Traits\EnumHelpers;

enum UserNotificationTypeEnum: int
{
    use EnumHelpers;

    case INQUIRIES = 1;
    case INVOICES = 2;
    case TRANSACTIONS = 3;
    case SYSTEM = 4;
    case LOW_STOCK = 5;


    public static function labels(): array
    {
        return [
            "inquiries",
            "invoices",
            "transactions",
            "system",
            "low_stock",
        ];
    }

    public function label(): string
    {
        return match ($this) {
            self::INQUIRIES => 'inquiries',
            self::INVOICES => 'invoices',
            self::TRANSACTIONS => 'transactions',
            self::SYSTEM => 'system',
            self::LOW_STOCK => 'low_stock',
        };
    }


    public static function fromString(string $value): self
    {
        return match (strtolower($value)) {
            'inquiries' => self::INQUIRIES,
            'invoices' => self::INVOICES,
            'transactions' => self::TRANSACTIONS,
            'system' => self::SYSTEM,
            'low_stock' => self::LOW_STOCK,
        };
    }
}
