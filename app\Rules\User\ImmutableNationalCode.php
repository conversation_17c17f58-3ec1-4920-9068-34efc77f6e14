<?php

namespace App\Rules\User;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * Immutable National Code Rule
 *
 * Validates that national_code cannot be changed once it's set for a user.
 */
class ImmutableNationalCode implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param string $attribute
     * @param mixed $value
     * @param Closure $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = auth()->user();
        
        // If user doesn't exist (shouldn't happen in this context), allow the validation
        if (!$user) {
            return;
        }
        
        // If user already has a national_code set and it's different from the new value
        if (!empty($user->national_code) && $user->national_code !== $value) {
            $fail($this->message());
        }
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return __('messages.user.national_code_immutable');
    }
}
