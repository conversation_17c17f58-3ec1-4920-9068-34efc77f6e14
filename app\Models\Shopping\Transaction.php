<?php

namespace App\Models\Shopping;

use App\Enums\PayTypeEnum;
use App\Enums\Product\StatusEnum;
use App\Traits\Transaction\TransactionRelationsTrait;
use App\Traits\Transaction\TransactionAttributesTrait;
use Illuminate\Database\Eloquent\Model;

/**
 * Transaction Model
 *
 * Represents a payment transaction in the system. Can be associated with different entities
 * through a polymorphic relationship (invoice payments, wallet top-ups, etc.).
 *
 * @property string $id The unique identifier for the transaction
 * @property string $user_id The ID of the user who owns this transaction
 * @property string $payable_id The ID of the related model (invoice, user, etc.)
 * @property string $payable_type The class name of the related model (Invoice, User, etc.)
 * @property string $status The status of the transaction (pending, rejected, paid)
 * @property string|null $track_code The payment tracking code
 * @property float $amount The transaction amount
 * @property string $payment_method How the payment was made (wallet, online, etc.)
 * @property string|null $payment_gateway The payment gateway used (stripe, zarinpal, etc.)
 * @property string|null $authority The payment authority code from payment gateway
 * @property string|null $ref_id The reference ID from payment gateway
 * @property string|null $description The description of the transaction
 * @property \Carbon\Carbon|null $paid_at When the payment was completed
 * @property \Carbon\Carbon $created_at When the transaction was created
 * @property \Carbon\Carbon $updated_at When the transaction was last updated
 * @property-read \Illuminate\Database\Eloquent\Model $payable The model this transaction belongs to
 */
class Transaction extends Model
{
    use TransactionRelationsTrait, TransactionAttributesTrait;




    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'user_id',
        'payable_id',
        'payable_type',
        'status',
        'track_code',
        'amount',
        'payment_method',
        'payment_gateway',
        'authority',
        'ref_id',
        'description',
        'paid_at',
        'source',
        'terminal_id',
        'ip',
        'card_number',
        'card_hash',
        'fee',
        'fee_type',
        'shaparak_fee',
        'order_id',
        'wages',
        'code',
        'price_unit_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'user_id' => 'string',
        'payable_id' => 'string',
        'payable_type' => 'string',
        'amount' => 'float',
        'paid_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'status' => PayTypeEnum::class,
    ];
}
