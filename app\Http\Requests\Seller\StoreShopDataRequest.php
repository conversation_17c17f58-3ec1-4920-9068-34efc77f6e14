<?php

namespace App\Http\Requests\Seller;

use Illuminate\Foundation\Http\FormRequest;

class StoreShopDataRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Set to false only if you want to restrict who can send this
    }

    public function rules(): array
    {
        return [
            'shop_id' => 'required|string|exists:shops,id',
            'seller_type' => 'required|in:0,1',
            'description' => 'required|string|max:1000',
            'phone' => 'required|string|regex:/^09\d{9}$/',
            'email' => 'required|email|max:255',
            'image' => 'required|image|max:5120',
            'address' => 'required|string|max:500',
            'province_id' => 'required|exists:cities,id',
            'city_id' => 'required|exists:cities,id',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ];
    }


}