<?php

namespace App\Http\Controllers\Api\V1\Private\Seller\Shop;

use App\Http\Controllers\Controller;
use App\Http\Requests\Seller\StoreShopDataRequest;
use App\Service\Actions\Shop\StoreShopData;
use Illuminate\Http\Request;

class StoreShopDataController extends Controller
{
    public function store(StoreShopDataRequest $request, StoreShopData $action)
    {
        $data = $action->handle($request->validated());

    }
}
