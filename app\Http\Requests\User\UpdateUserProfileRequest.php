<?php

namespace App\Http\Requests\User;

use App\Rules\JalaliDateRule;
use App\Rules\MinJalaliAge;
use App\Rules\User\ImmutableNationalCode;
use Illuminate\Foundation\Http\FormRequest;

/**
 * Get User Profile Request
 *
 * Validates the request data for retrieving a user's profile.
 */
class UpdateUserProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only authenticated users can view their profile
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'full_name' => 'sometimes|string|max:50',
            'email' => 'sometimes|email',
            'national_code' => [
                'sometimes',
                'string',
                'size:10',
                new ImmutableNationalCode(),
            ],
            'shamsi_birth_date' => [
                'sometimes',
                new JalaliDateRule(),
            ],
            'profile_image' => 'sometimes|image|max:5000',
        ];
    }

}
