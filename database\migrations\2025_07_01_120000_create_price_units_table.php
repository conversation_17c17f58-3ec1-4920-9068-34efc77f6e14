<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for creating the price_units table.
 * This table stores currency/price units (تومان, ریال, دلار, etc.)
 */
return new class extends Migration {
    public function up(): void
    {
        Schema::create('price_units', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., 'تومان', 'ریال', 'دلار'
            $table->string('symbol')->nullable(); // e.g., '﷼', '$', '€'
            $table->string('code', 3)->unique(); // e.g., 'IRR', 'USD', 'EUR'
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false); // Only one can be default
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('price_units');
    }
};
