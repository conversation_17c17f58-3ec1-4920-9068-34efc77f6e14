<?php

use App\Http\Middleware\BlockWebRoutes;
use App\Http\Middleware\CheckApplicationToken;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api/v1/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'jwt' => \App\Http\Middleware\JwtMiddleware::class,
            'login' => \App\Http\Middleware\LoginUserMiddleware::class,
            'verify.torob.token' => \App\Http\Middleware\Torob\TorobApiMiddleware::class,

        ]);
        $middleware->group('api', [
            CheckApplicationToken::class,
        ]);
        $middleware->group('web', [
            BlockWebRoutes::class,
        ]);


    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Register the custom exception handler for API requests
        $exceptions->renderable(function (\Throwable $e, \Illuminate\Http\Request $request) {
            if ($request->expectsJson()) {
                $apiHandler = new \App\Exceptions\ApiExceptionHandler();
                return $apiHandler->handle($e, $request);
            }
        });
    })->create();
