<?php

namespace App\Models\UserInteraction;

use App\Enums\Product\StatusEnum;
use App\Traits\Models\Helpers\ShamsiCraetedDate;
use App\Traits\Question\QuestionRelationsTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class Question extends Model
{
    use QuestionRelationsTrait, ShamsiCraetedDate;

    protected $fillable = ['body', 'user_id', 'status'];
    protected $casts = [
        'status' => StatusEnum::class
    ];

    public function scopeSearch(Builder $query, ?string $term): Builder
    {
        if (blank($term)) {
            return $query;
        }

        return $query->where(function (Builder $q) use ($term) {
            $q->where('body', 'like', "%{$term}%")
                ->orWhereHas('answers', fn(Builder $a) => $a->where('body', 'like', "%{$term}%"));
        });
    }

    /** Keep only questions that have at least one answer */
    public function scopeAnswered(Builder $query): Builder
    {
        return $query->whereHas('answers');
    }

    /** Keep only questions that have NO answers */
    public function scopeUnanswered(Builder $query): Builder
    {
        return $query->whereDoesntHave('answers');
    }

    /** Generic status filter so we can reuse it elsewhere */
    public function scopeStatus(Builder $query, ?StatusEnum $status): Builder
    {
        return is_null($status) ? $query : $query->where('status', $status);
    }

}
