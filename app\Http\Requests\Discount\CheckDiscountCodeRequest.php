<?php

namespace App\Http\Requests\Discount;

use App\Models\Discount\DiscountCode;
use Illuminate\Foundation\Http\FormRequest;

class CheckDiscountCodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'code' => 'string|required',
        ];
    }
    public function withValidator($validator)
    {

        $validator->after(function ($validator) {
            $user = auth()->user();
            $cart = $user->cart;

            // if (!$cart || $cart->items()->count() == 0) {
            //     $validator->errors()->add('cart', __('messages.cart.cart_empty'));
            //     return;
            // }

            // $cartTotal = $cart->items->sum(function ($item) {
            //     return $item->effective_price;
            // });

            // if ($cartTotal <= 0) {
            //     $validator->errors()->add('cart', __('messages.cart.cart_empty'));
            //     return;
            // }
            $discountCode = DiscountCode::where('code', $this->input('code'))->first();

            if (!$discountCode || !$discountCode->isValid()) {
                $validator->errors()->add('discount code', __('messages.discount_code.not_valid'));
                return;
            }


        });
    }
}
