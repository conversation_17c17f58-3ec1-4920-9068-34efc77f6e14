<?php

namespace App\Models\Product;

use App\Enums\Product\StatusEnum;
use App\Traits\Product\ProductRelationsTrait;
use App\Traits\Product\ProductAttributesTrait;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use ProductRelationsTrait, ProductAttributesTrait;



    protected $fillable = [
        'title',
        'description',
        'slug',
        'meta_title',
        'meta_description',
        'delivery_method_ids',
        'shop_id',
        'rate',
        'seller_status',
        'product_unit_id',
        'admin_status',
        'category_id'
    ];

    protected $casts = [
        'status' => StatusEnum::class,
    ];

    public function scopeWithSellerResourceDefaults()
    {
        $this->load('gallery');
    }
    /**
     * Format search term for PostgreSQL full-text search
     */
    private function formatSearchTerm($term)
    {
        // Remove special characters and normalize
        $term = preg_replace('/[^\w\s]/', ' ', $term);
        $term = preg_replace('/\s+/', ' ', trim($term));

        return $term;
    }

    /**
     * Get search suggestions based on existing titles
     */
    public static function getSearchSuggestions($searchTerm, $limit = 5)
    {
        if (empty($searchTerm)) {
            return collect();
        }

        return self::select('title')
            ->where('title', 'ILIKE', "%{$searchTerm}%")
            ->distinct()
            ->limit($limit)
            ->pluck('title');
    }
}
