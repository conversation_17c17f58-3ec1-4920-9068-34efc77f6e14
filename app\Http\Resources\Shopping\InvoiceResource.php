<?php

namespace App\Http\Resources\Shopping;

use App\Enums\Product\StatusEnum;
use App\Models\Product\PriceUnit;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Shopping\InvoiceProductResource;
use App\Http\Resources\Shopping\TransactionResource;
use Illuminate\Http\Resources\MissingValue;

/**
 * Resource class for transforming Invoice models into API responses.
 *
 * Provides a representation of an invoice with its ID, status, creation date, and transaction details.
 */
class InvoiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Invoice ID
     * - Invoice Number
     * - Status (pending, paid, rejected)
     * - Creation date
     * - Products in the invoice
     * - Pricing information (subtotal, total_discount, total) matching cart structure
     * - Address information (receiver name, phone, address, province, city, zip code)
     * - Transaction details
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $products = $this->whenLoaded('products');
        $productCount = 0;
        foreach ($products as $product) {
            $productCount += $product->quantity;
        }


        return [
            'id' => (string) $this->id,
            'invoice_number' => $this->invoice_number,
            'status' => $this->status->label(),
            'creation_date' => $this->ShamsiCreatedAt,
            'products' => InvoiceProductResource::collection($products),
            'products_count' => $productCount,
            // Pricing information matching cart structure
            'subtotal' => $this->subtotal,
            'total_discount' => $this->discount_amount,
            'total' => $this->total,
            'price_unit' => $this->price_unit ?? PriceUnit::getDefault()?->name,

            // Address information
            'address' => [
                'receiver_name' => $this->receiver_name,
                'receiver_phone' => $this->receiver_phone,
                'address' => $this->address,
                'province' => $this->province,
                'city' => $this->city,
                'zip_code' => $this->zip_code,
                'latitude' => $this->latitude,
                'longitude' => $this->longitude,
            ],
            // Transaction details
            'delivery_status' => $this->delivery_status->label(),
            'transactions' => $this->whenLoaded('transactions', fn() => TransactionResource::collection($this->transactions), []),
            'pay_method' => $this->calcPayMethod(),
            'discount_code' => $this->discountCode?->code,
        ];
    }

    private function calcPayMethod(): string
    {
        // If transactions aren’t eager-loaded, assume plain online payment
        if (!$this->relationLoaded('transactions')) {
            return 'پرداخت آنلاین';
        }

        // Pick distinct payment methods where status = 1
        $methods = $this->transactions
            ->where('status', 1)
            ->pluck('payment_method')
            ->unique();

        // One method only → map directly
        if ($methods->count() === 1) {
            return $methods->first() === 'wallet'
                ? 'کیف پول'
                : 'پرداخت آنلاین';
        }

        // Both wallet & online present
        if ($methods->contains('wallet') && $methods->contains('online')) {
            return 'کیف پول+پرداخت آنلاین';
        }

        // Anything else
        return 'پرداخت نشده';
    }

}
