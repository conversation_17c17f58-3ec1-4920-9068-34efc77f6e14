<?php

namespace App\Models\User;

use App\Traits\WalletTransaction\WalletTransactionRelationsTrait;
use App\Traits\WalletTransaction\WalletTransactionAttributesTrait;
use Illuminate\Database\Eloquent\Model;

/**
 * WalletTransaction Model
 *
 * Represents a transaction in a user's wallet.
 *
 * @property string $id The unique identifier for the transaction
 * @property string $user_id The ID of the user this transaction belongs to
 * @property string $type The type of transaction (deposit, withdraw)
 * @property float $amount The amount of the transaction

 * @property string|null $referenceable_id The ID of the related entity (invoice, transaction, etc.)
 * @property string|null $referenceable_type The type of the related entity (Invoice, Transaction, etc.)
 * @property string|null $description The description of the transaction
 * @property array|null $meta Additional metadata
 * @property \Carbon\Carbon $created_at When the transaction was created
 * @property \Carbon\Carbon $updated_at When the transaction was last updated
 * @property-read \App\Models\User\User $user The user this transaction belongs to
 */
class WalletTransaction extends Model
{
    use WalletTransactionRelationsTrait, WalletTransactionAttributesTrait;




    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'user_id',
        'type',
        'amount',
        'referenceable_id',
        'referenceable_type',
        'description',
        'meta',
        'price_unit_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'user_id' => 'string',
        'amount' => 'float',
        'meta' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Transaction types
     */
    const TYPE_DEPOSIT = 'deposit';
    const TYPE_WITHDRAW = 'withdraw';
}
