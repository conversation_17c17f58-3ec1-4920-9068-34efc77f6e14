<?php

namespace App\Http\Controllers\Api\V1\Private\Seller\Questions;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\Seller\GetQuestionsRequest;
use App\Http\Resources\Core\PaginatedResourceCollection;
use App\Http\Resources\UserInteraction\QuestionResource;
use App\Services\Actions\Seller\Questions\GetQuestions;

class QuestionController extends BaseController
{
    /**
     * Get questions present for a seller
     * @param \App\Http\Requests\Seller\GetQuestionsRequest $request
     * @param \App\Services\Actions\Seller\Questions\GetQuestions $action
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(GetQuestionsRequest $request, GetQuestions $action)
    {
        $questions = $action->handle($request->validated());

        return $this->sendResponse(
            new PaginatedResourceCollection(
                $questions,
                'questions',
                QuestionResource::class
            ),
            __('messages.question.found')
        );

    }
}
