<?php

namespace App\Http\Resources\UserInteraction;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming Answer models into API responses.
 *
 * Provides a representation of an answer with its body, creation date, and user information.
 */
class AnswerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Answer ID
     * - Answer body
     * - Creation date
     * - User information (if available)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->uuid,
            'body' => $this->body,
            'created_at' => $this->shamsiCreatedAt,
        ];

        // Add user information if the user relationship is loaded
        if ($this->relationLoaded('user') && $this->user) {
            $data['user'] = [
                'name' => $this->user->full_name ?? 'کاربر ناشناس',
            ];
        }

        return $data;
    }
}
