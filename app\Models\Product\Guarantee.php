<?php

namespace App\Models\Product;

use App\Traits\Guarantee\GuaranteeRelationsTrait;
use Illuminate\Database\Eloquent\Model;

class Guarantee extends Model
{
    use GuaranteeRelationsTrait;

    protected $fillable = [
        'price',
        'months',
        'company_name',
        'price_unit_id',
    ];

    protected $casts = [
        'price' => 'float',
        'months' => 'integer',
    ];

}
