<?php

namespace App\Http\Middleware;

use App\Http\Controllers\Api\BaseController;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class BlockWebRoutes
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle($request, Closure $next)
    {

        if ($request->is('docs/api') && config('app.doc_api_key')) {
            if ($request->input('X-DOC-TOKEN') == config('app.doc_api_key'))
                return $next($request);
        }
        return response('', 204); // or 200

    }
}
