<?php

namespace App\Traits\ProductVariant;

use App\Models\Product\Product;
use App\Models\Product\PriceUnit;
use App\Models\Product\ProductsReservation;
use App\Models\Product\PurchaseEntry;
use App\Models\Product\VariationAttribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * ProductVariation Relations Trait
 *
 * This trait contains all relationship methods for the ProductVariation model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\ProductVariation
 */
trait ProductVariantRelationsTrait
{
    /**
     * Get the product that this variation belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the attributes associated with this variation.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function attributes()
    {
        return $this->hasMany(VariationAttribute::class, );
    }

    /**
     * Get the purchase entries associated with this variation.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function purchases()
    {
        return $this->hasMany(PurchaseEntry::class);
    }

    /**
     * Get the active reservations associated with this variation.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function reservations()
    {
        return $this->hasMany(ProductsReservation::class);
    }

    /**
     * Get the price unit associated with this variation.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function priceUnit()
    {
        return $this->belongsTo(PriceUnit::class);
    }
}
