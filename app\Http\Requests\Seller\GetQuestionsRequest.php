<?php

namespace App\Http\Requests\Seller;

use App\Enums\Product\StatusEnum;
use App\Enums\Question\QuestionFilterEnum;
use Illuminate\Foundation\Http\FormRequest;

class GetQuestionsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'page' => 'sometimes|integer|min:1',
            'limit' => 'sometimes|integer|min:1|max:20',
            'filter' => [
                'sometimes',
                QuestionFilterEnum::rule()
            ],
            'status' => [
                'sometimes',
                StatusEnum::rule()
            ],

            'search' => [
                'sometimes',
                'string',
                'max:30'
            ]
        ];
    }
}
