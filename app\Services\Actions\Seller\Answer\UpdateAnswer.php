<?php

namespace App\Services\Actions\Seller\Answer;

use App\Models\UserInteraction\Answer;
use App\Models\UserInteraction\Question;

class UpdateAnswer
{

    /**
     * update answer for a question
     * @param array $data
     */
    public function handle(array $data)
    {
        $userId = auth()->id();
        $answer = $data['model']->load('question');

        $answer->update([
            'body' => $data['body'],
            'user_id' => $userId
        ]);

        return $answer->question->load('answers');
    }
}
