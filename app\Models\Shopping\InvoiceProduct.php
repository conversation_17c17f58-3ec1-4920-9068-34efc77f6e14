<?php

namespace App\Models\Shopping;

use App\Traits\InvoiceProduct\InvoiceProductRelationsTrait;
use App\Traits\InvoiceProduct\InvoiceProductAttributesTrait;
use Illuminate\Database\Eloquent\Model;

/**
 * InvoiceProduct Model
 *
 * Represents a product in an invoice with details and quantity.
 *
 * @property string $id The unique identifier for the invoice product
 * @property string $invoice_id The ID of the invoice this product belongs to
 * @property string $product_id The ID of the product
 * @property string $variant_id The ID of the product variation
 * @property string $name The name/title of the product (snapshot)
 * @property float $price The regular price of the product variation (snapshot)
 * @property float|null $sale_price The sale price of the product variation (snapshot)
 * @property int $quantity The quantity of this product in the invoice
 * @property string|null $image The image URL for this product (snapshot)
 * @property string|null $shop_id The ID of the shop this product belongs to (snapshot)
 * @property string|null $shop_name The name of the shop this product belongs to (snapshot)
 * @property-read float $total The calculated total price (considering sale price if available)
 * @property-read Invoice $invoice The invoice this product belongs to
 * @property-read \Illuminate\Database\Eloquent\Collection|InvoiceProductDetail[] $details The details of this invoice product
 */
class InvoiceProduct extends Model
{
    use InvoiceProductRelationsTrait, InvoiceProductAttributesTrait;



    protected $fillable = [
        'invoice_id',
        'product_id',
        'product_variant_id',
        'name',
        'price',
        'sale_price',
        'quantity',
        'image',
        'sku',
        'guarantee_id',
        'guarantee_company_name',
        'guarantee_months',
        'guarantee_price',
        'shop_id',
        'shop_name',
        'price_unit_id',
    ];
}
