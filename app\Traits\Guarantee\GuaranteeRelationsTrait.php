<?php

namespace App\Traits\Guarantee;

use App\Models\Product\Product;
use App\Models\Product\PriceUnit;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * Guarantee Relations Trait
 *
 * This trait contains all relationship methods for the Guarantee model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Guarantee
 */
trait GuaranteeRelationsTrait
{
    /**
     * Get the products that this guarantee belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'guarantee_product', 'guarantee_id', 'product_id');
    }

    /**
     * Get the price unit associated with this guarantee.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function priceUnit(): BelongsTo
    {
        return $this->belongsTo(PriceUnit::class);
    }
}
