<?php

namespace App\Http\Resources\Product;

use App\Models\Product\PriceUnit;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming Guarantee models into API responses.
 */
class GuaranteeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Guarantee ID
     * - Price
     * - Duration in months
     * - Company name
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (string) $this->id,
            'price' => $this->price,
            'price_unit' => $this->whenLoaded('priceUnit', fn() => $this->priceUnit?->name) ?? PriceUnit::getDefault()?->name,
            'months' => $this->months,
            'company_name' => $this->company_name,
        ];
    }
}
