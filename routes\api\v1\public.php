<?php
/**
 * Public API Routes
 *
 * This file contains all public API routes that do not require authentication.
 * These endpoints are accessible to all users without authentication.
 *
 * Base URI: /api/v1
 */

use App\Http\Controllers\Api\V1\Private\Discount\CheckDiscountCodeController;
use App\Http\Controllers\Api\V1\Private\SellerAccounting\SupportingBankController;
use App\Http\Controllers\Api\V1\Public\Comment\ProductCommentController;
use App\Http\Controllers\Api\V1\Private\Torob\TorobController;
use App\Http\Controllers\Api\V1\Public\Categories\CategoryController;
use App\Http\Controllers\Api\V1\Public\Categories\CategorySearchableFieldsController;
use App\Http\Controllers\Api\V1\Public\Categories\CategoryTreeController;
use App\Http\Controllers\Api\V1\Public\Categories\CategoryBreadCrumbController;
use App\Http\Controllers\Api\V1\Public\CityController;
use App\Http\Controllers\Api\V1\Public\Product\ProductController;
use App\Http\Controllers\Api\V1\Public\Product\SimilarProductController;
use App\Http\Controllers\Api\V1\Public\Product\AttributeController;
use App\Http\Controllers\Api\V1\Public\Product\ProductCategoryController;
use App\Http\Controllers\Api\V1\Public\Product\GuaranteeController;
use App\Http\Controllers\Api\V1\Public\Content\ArticleController;
use App\Http\Controllers\Api\V1\Public\Content\GuideController;
use App\Http\Controllers\Api\V1\Public\Shopping\VerifyTransactionController;
use App\Http\Controllers\Api\V1\Public\UserInteraction\CommentController;
use App\Http\Controllers\Api\V1\Public\UserInteraction\QuestionController;
use App\Http\Controllers\Api\V1\Public\Notification\PushNotificationController;
use App\Http\Controllers\Api\V1\Public\Notification\TopicSubscriptionController;
use App\Http\Controllers\Api\V1\Public\Comment\ArticleCommentController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\Public\Notification\ClientTokenController;
use App\Http\Controllers\Ticket\TicketDepartmentController;
use App\Models\Ticket\TicketDepartment;
use App\Services\Actions\Discount\CheckDiscountCode;

/**
 * Product routes
 *
 * @unauthenticated
 */
Route::resource('products', ProductController::class)->only([
    'index',
    'show'
])->parameters([
            'products' => 'slug'
        ]);

/**
 * Product related resources routes
 *
 * @unauthenticated
 */
// Articles related to a product
Route::resource('products.articles', ArticleController::class)->only([
    'index'
])->shallow()->parameters([
            'products' => 'slug'
        ]);
// Guides related to a product
Route::resource('products.guides', GuideController::class)->only([
    'index'
])->shallow()->parameters([
            'products' => 'slug'
        ]);

// Comments related to a product
Route::resource('products.comments', CommentController::class)->only([
    'index'
])->shallow()->parameters([
            'products' => 'slug'
        ]);

// Questions related to a product
Route::resource('products.questions', QuestionController::class)->only([
    'index'
])->shallow()->parameters([
            'products' => 'slug'
        ]);

/**
 * Similar products
 *
 * @unauthenticated
 */
Route::resource('products.similar', SimilarProductController::class)->only([
    'index'
])->parameters([
            'products' => 'slug'
        ]);

/**
 * Product guarantees
 *
 * @unauthenticated
 */
Route::resource('products.guarantees', GuaranteeController::class)->only([
    'index'
])->parameters([
            'products' => 'slug'
        ]);


/**
 * Verify transaction route
 *
 * @unauthenticated
 */
Route::resource('verify-transaction', VerifyTransactionController::class)
    ->only(['store']);


/**
 * Product attributes routes
 *
 * @unauthenticated
 */
Route::resource('attributes', AttributeController::class)
    ->only(['index']);



/**
 * Category tree
 *
 * @unauthenticated
 */
Route::get('categories/tree', [CategoryTreeController::class, 'index'])
    ->name('categories.tree.index');

Route::get('categories/{category}/tree', [CategoryTreeController::class, 'show'])
    ->name('categories.tree.show');

/**
 * Category routes
 *
 * @unauthenticated
 */
Route::resource('categories', CategoryController::class)
    ->only(['index', 'show'])
    ->parameters(['categories' => 'slug']);


/**
 * Category products routes
 *
 * @unauthenticated
 */
Route::get('categories/{slug}/products', [ProductCategoryController::class, 'index'])
    ->name('categories.products.index');

Route::resource('categories.searchables', CategorySearchableFieldsController::class)
    ->only(['index'])
    ->parameters(['categories' => 'slug']);


Route::resource('notifications', PushNotificationController::class)->only(['store']);
Route::resource('topic-subscriptions', TopicSubscriptionController::class)->only(['store']);



Route::middleware('verify.torob.token')->group(function () {
});
/**
 * torob products routes
 *
 * @unauthenticated
 */
Route::resource('torob', TorobController::class)->only(['store']);
/**
 * Category breadcrumb routes
 *
 * @unauthenticated
 */
Route::resource("categories.breadcrumb", CategoryBreadCrumbController::class)->only(['index']);
/**
 * article comments routes
 *
 * @unauthenticated
 */
Route::resource("article/comments", ArticleCommentController::class)->only(['store'])->middleware('login');
/**
 * products comments routes
 *
 * @unauthenticated
 */
Route::resource('products.comments', ProductCommentController::class)->only([
    'index'
]);

/**
 * @unauthenticated
 */
Route::resource("products/comments", ProductCommentController::class)->only(['store'])->middleware('login');
;

/**
 * article comments routes
 *
 * @unauthenticated
 */
Route::resource('article.comments', ArticleCommentController::class)->only([
    'index'
]);

Route::resource('client-tokens', ClientTokenController::class)->only(['store']);


/**
 * ticket department routes
 *
 * @unauthenticated
 */
Route::resource('ticket-departments', TicketDepartmentController::class)->only(['index']);


/**
 * discount codes check route
 *
 * @unauthenticated
 */
Route::resource("discount-codes/check", CheckDiscountCodeController::class)->only(['store'])->middleware('login');


Route::resource("seller-account/supporting-banks", SupportingBankController::class)->only(['index']);


Route::resource("cities", CityController::class)->only(['index', 'show']);

