<?php

namespace App\Http\Resources\Product;

use App\Models\Product\PriceUnit;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming Product models into API responses for product listing.
 *
 * This resource is specifically designed for product index/listing pages where we need
 * minimal product information for performance optimization.
 */
class ProductListItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Basic product information (title, slug)
     * - Product rating
     * - Main product image
     * - Price information (regular and sale price)
     * - Categories
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        // Get the default variation for pricing
        $defaultVariation = $this['variants'][0];
        // Use stored product rating

        // Get the main image
        $mainImage = (isset($this['gallery']) && isset($this['gallery'][0])) ? $this['gallery'][0] : null;

        // Determine the display price (use sale_price if available and lower than regular price)
        $price = $defaultVariation['price'];
        $salePrice = isset($defaultVariation['sale_price']) ? $defaultVariation['sale_price'] : null;
        $displayPrice = ($salePrice && $salePrice < $price) ? $salePrice : $price;
        $inStock = collect($this['variants'])->contains(function ($variant) {
            return $variant['stock'] > 1;
        });
        return [
            'title' => $this['title'],
            'slug' => $this['slug'],
            'rate' => (float) $this['rate'],
            'image' => $mainImage ? [
                'url' => $mainImage['image_url'],
                'caption' => $mainImage['caption'],
            ] : null,
            'price' => (int) $displayPrice,
            'actualy_price' => (int) $price,
            'price_unit' => $defaultVariation['price_unit'] ?? PriceUnit::getDefault()?->name,
            'in_stock' => $inStock,
            'sale_price' => $salePrice && $salePrice < $price ? (int) $salePrice : null,
        ];
    }
}
