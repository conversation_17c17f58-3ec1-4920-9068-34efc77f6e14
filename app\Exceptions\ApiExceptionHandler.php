<?php

namespace App\Exceptions;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\Shopping\VerifiedTransactionResource;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

/**
 * API Exception Handler
 *
 * Handles all exceptions for API requests and formats them into consistent responses.
 */
class ApiExceptionHandler
{
    /**
     * The base controller instance for formatting responses.
     *
     * @var \App\Http\Controllers\Api\BaseController
     */
    protected $controller;

    /**
     * Create a new API exception handler instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->controller = new BaseController();
    }

    /**
     * Handle the exception and return a JSON response.
     *
     * @param \Throwable $exception The exception to handle
     * @param \Illuminate\Http\Request $request The current request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handle(Throwable $exception, Request $request)
    {
        if ($exception instanceof ValidationException) {
            return $this->handleValidationException($exception);
        }

        if ($exception instanceof PaymentPreprocessException) {
            return $this->handlePaymentPreprocessException($exception);
        }
        if ($exception instanceof PaymentFailException) {
            return $this->handlePaymentFailException($exception);
        }


        if ($exception instanceof BusinessException) {
            return $this->handleBusinessException($exception);
        }

        if ($exception instanceof ModelNotFoundException) {
            return $this->handleModelNotFoundException($exception);
        }
        if ($exception instanceof NotFoundHttpException) {
            return $this->handleNotFoundHttpException($exception);
        }

        if ($exception instanceof AuthenticationException) {
            return $this->handleAuthenticationException($exception);
        }

        if ($exception instanceof AuthorizationException) {
            return $this->handleAuthorizationException($exception);
        }
        return $this->handleGenericException($exception);
    }

    /**
     * Handle validation exceptions.
     *
     * @param \Illuminate\Validation\ValidationException $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleValidationException(ValidationException $exception)
    {

        return $this->controller->sendError(
            ['errors' => $exception->errors()],
            __('messages.common.validation_error'),
            $exception->status
        );
    }

    /**
     * Handle business exceptions.
     *
     * @param \App\Exceptions\BusinessException $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleBusinessException(BusinessException $exception)
    {
        return $this->controller->sendError(
            $exception->getErrorData(),
            $exception->getMessage(),
            $exception->getStatusCode()
        );
    }

    /**
     * Handle model not found exceptions.
     *
     * @param \Illuminate\Database\Eloquent\ModelNotFoundException $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleModelNotFoundException(ModelNotFoundException $exception)
    {

        $model = basename(str_replace('\\', '/', $exception->getModel()));
        $model = strtolower($model);
        return $this->controller->sendError(
            ['model' => $model, 'id' => $exception->getIds()[0]],
            __('messages.common.model_not_found', ['model' => __("validation.attributes." . $model), 'id' => $exception->getIds()[0]]),
            Response::HTTP_NOT_FOUND
        );
    }

    /**
     * Handle authentication exceptions.
     *
     * @param \Illuminate\Auth\AuthenticationException $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleAuthenticationException(AuthenticationException $exception)
    {
        return $this->controller->sendError(
            ['message' => $exception->getMessage()],
            __('messages.common.unauthorized'),
            Response::HTTP_UNAUTHORIZED
        );
    }

    /**
     * Handle authorization exceptions.
     *
     * @param \Illuminate\Auth\Access\AuthorizationException $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleAuthorizationException(AuthorizationException $exception)
    {
        return $this->controller->sendError(
            ['message' => $exception->getMessage()],
            __('messages.common.forbidden'),
            Response::HTTP_FORBIDDEN
        );
    }

    /**
     * Handle payment preprocess exceptions.
     *
     * @param \App\Exceptions\PaymentPreprocessException $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handlePaymentPreprocessException(PaymentPreprocessException $exception)
    {
        return $this->controller->sendError(
            $exception->getErrorData(),
            $exception->getMessage(),
            $exception->getStatusCode()
        );
    }
    protected function handlePaymentFailException(PaymentFailException $exception)
    {
        return $this->controller->sendError(
            new VerifiedTransactionResource($exception->getErrorData()['model']),
            $exception->getMessage(),
            $exception->getStatusCode()
        );
    }

    /**
     * Handle generic exceptions.
     *
     * @param \Throwable $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleGenericException(Throwable $exception)
    {
        $statusCode = method_exists($exception, 'getStatusCode')
            ? $exception->getStatusCode()
            : Response::HTTP_INTERNAL_SERVER_ERROR;

        return $this->controller->sendError(
            ['message' => $exception->getMessage()],
            __('messages.common.server_error'),
            $statusCode
        );
    }

    /**
     * Handle model not found exceptions.
     *
     * @param \Symfony\Component\HttpKernel\Exception\NotFoundHttpException $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleNotFoundHttpException(NotFoundHttpException $exception)
    {
        $previous = $exception->getPrevious();
        if ($previous instanceof ModelNotFoundException) {
            return $this->handleModelNotFoundException($previous);
        }

        return $this->controller->sendError(
            ['message' => $exception->getMessage()],
            __('messages.common.not_found'),
            Response::HTTP_NOT_FOUND
        );

    }
}
