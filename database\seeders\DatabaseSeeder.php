<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

/**
 * Main database seeder that orchestrates the execution of all other seeders.
 */
class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * Executes all seeders in the correct order to ensure proper data relationships.
     * The order is important to maintain referential integrity.
     */
    public function run(): void
    {
        $this->call([
                // Base entities (no dependencies)
            PriceUnitSeeder::class,
            ShopSeeder::class,
            UserSeeder::class,
            CategorySeeder::class,
            DeliveryMethodSeeder::class,

                // User-Shop relationships
            ShopUserSeeder::class,

                // Products and basic product data
            ProductSeeder::class,
            ProductDetailSeeder::class,
            KeywordSeeder::class,

                // Product variants (depends on products)
            ProductVariantSeeder::class,         // Creates default variants for products without variants

                // Inventory and pricing (depends on variants)
            PurchaseEntrySeeder::class,
            SalePriceSeeder::class,

                // Product relationships and content
            ProductCategoriesSeeder::class,      // Attaches categories to products
            ProductCommentsSeeder::class,
            ProductQuestionsSeeder::class,
            GuaranteeSeeder::class,
            ArticleSeeder::class,
            GuideSeeder::class,

                // User addresses (depends on users)
            AddressSeeder::class,

                // Comprehensive data seeder (should be last as it may override/recreate data)
            ClothProductDataSeeder::class,

                // Attributes (depends on categories, should be last)
            AttributeSeeder::class,
            VariantSeeder::class,              // Creates variants for cloth product
            UpdateVariationStockSeeder::class,
            UserNotificationSeeder::class,
            TicketDepartmentSeeder::class,
            TicketSeeder::class,
        ]);
    }
}
