<?php

namespace App\Services\Actions\Notification;

use App\Models\Product\ProductVariant;
use App\Services\Notifications\UserNotificationService;

/**
 * Send Low Stock Notification Action
 *
 * Handles sending notifications to sellers when their product variant stock falls below 3.
 */
class SendLowStockNotification
{
    public function __construct(
        private UserNotificationService $notificationService
    ) {
    }

    /**
     * Handle sending low stock notification for a product variant.
     *
     * @param array $data Contains product_variant_id
     * @return bool
     */
    public function handle(array $data): bool
    {
        $variant = ProductVariant::with(['product.shop.users'])
            ->find($data['product_variant_id']);

        if (!$variant) {
            return false;
        }

        // Check if stock is actually below 3
        $currentStock = $variant->getCurrentQuantityAttribute();
        if ($currentStock >= 3) {
            return false;
        }

        // Get the sellers (users) associated with this product's shop
        $sellers = $variant->product->shop->users;

        if ($sellers->isEmpty()) {
            return false;
        }

        // Send notification to each seller
        foreach ($sellers as $seller) {
            $this->notificationService->sendLowStockAlert($variant, $seller, $currentStock);
        }

        return true;
    }
}
