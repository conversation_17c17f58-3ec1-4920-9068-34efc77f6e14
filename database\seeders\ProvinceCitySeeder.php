<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use App\Models\City;

class ProvinceCitySeeder extends Seeder
{
    public function run(): void
    {
        $json = File::get(database_path('data/cities.json'));
        $locations = json_decode($json, true);

        // Wipe the table (remove if you need to keep existing data)
        City::truncate();

        DB::transaction(function () use ($locations) {
            foreach ($locations as $province) {
                // 1) create the province (root)
                $provinceModel = City::create([
                    'title' => $province['name'],
                    'parent_id' => null,
                ]);

                // 2) create all its cities (children)
                foreach ($province['cities'] as $cityName) {
                    $provinceModel->children()->create([
                        'title' => $cityName,
                    ]);
                }
            }
        });
    }
}
