<?php

namespace App\Traits\Shop;

use App\Models\Content\Gallery;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * Shop Relations Trait
 *
 * This trait contains all relationship methods for the Shop model.
 * It helps to separate relationship logic from the core model functionality.
 *
 * @package App\Traits\Shop
 */
trait ShopRelationsTrait
{
    /**
     * Get the users that belong to this shop.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function users()
    {
        return $this->belongsToMany(User::class);
    }

    public function image(): MorphOne
    {
        return $this->morphOne(Gallery::class, 'imageable');
    }
}
