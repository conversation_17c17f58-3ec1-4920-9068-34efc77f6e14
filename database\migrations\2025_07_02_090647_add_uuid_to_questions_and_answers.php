<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            $table->uuid('uuid')
                ->default(DB::raw('gen_random_uuid()'))
                ->unique()
                ->after('id');
        });

        Schema::table('answers', function (Blueprint $table) {
            $table->uuid('uuid')
                ->default(DB::raw('gen_random_uuid()'))
                ->unique()
                ->after('id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            $table->dropColumn('uuid');
        });

        Schema::table('answers', function (Blueprint $table) {
            $table->dropColumn('uuid');
        });
    }
};
