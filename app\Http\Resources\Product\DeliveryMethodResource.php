<?php

namespace App\Http\Resources\Product;

use App\Models\Product\PriceUnit;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource class for transforming DeliveryMethod models into API responses.
 */
class DeliveryMethodResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * Includes:
     * - Delivery method ID
     * - Title
     * - Price
     * - Image URL
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (string) $this->id,
            'title' => $this->title,
            'price' => $this->price,
            'price_unit' => $this->whenLoaded('priceUnit', fn() => $this->priceUnit?->name) ?? PriceUnit::getDefault()?->name,
            'image_url' => $this->image_url,
        ];
    }
}
