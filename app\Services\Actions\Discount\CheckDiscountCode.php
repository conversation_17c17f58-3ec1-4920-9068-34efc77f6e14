<?php

namespace App\Services\Actions\Discount;

use App\Enums\Discount\DiscountTypeEnum;
use App\Exceptions\BusinessException;
use App\Models\Discount\DiscountCode;
use App\Traits\Disconunt\AppliesDiscountCode;

use function PHPUnit\Framework\isEmpty;

class CheckDiscountCode
{
    use AppliesDiscountCode;
    /**
     *updates discount code status
     * @param array $data
     */
    public function handle(array $data)
    {
        $user = auth()->user();
        $cart = $user->cart;

        $cartTotal = $cart->items->sum(function ($item) {
            return $item->total;
        });
        $cartSubTotal = $cart->items->sum(function ($item) {
            return $item->subtotal;
        });

        $totalDiscount = $cart->items->sum(function ($item) {
            return $item->discount;
        });

        [$discountAmount, $offPrice] = $this->applyDiscountToCart($data['code'], $cartTotal, 0);

        return [
            'subtotal' => $cartSubTotal,
            'total_discount' => $totalDiscount + $discountAmount,
            'total' => $offPrice,
            'off_amount' => $discountAmount,
        ];

    }
}
