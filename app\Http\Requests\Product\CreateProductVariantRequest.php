<?php

namespace App\Http\Requests\Product;

use App\Rules\ValidVariantAttributes;
use Illuminate\Foundation\Http\FormRequest;

class CreateProductVariantRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "product_slug" => "required|string|exists:products,slug",
            'attributes' => [
                'nullable',
                'array',
                new ValidVariantAttributes($this->input('product_slug')),
            ],
            'price' => "required|int|min:0",
            'sale_price' => "sometimes|int|lt:price",
            'price_unit_id' => "required|exists:price_units,id",
            "stock" => "required|int|min:0",

        ];
    }
}
