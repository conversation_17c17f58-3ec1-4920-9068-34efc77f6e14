<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('shops', function (Blueprint $table) {

            // نوع فروشنده: 0 = individual, 1 = legal entity
            $table->unsignedTinyInteger('seller_type')->after('id')->default(0);

            $table->text('description')->nullable();

            $table->string('phone', 20)->nullable();
            $table->string('email')->nullable();


            $table->text('address')->nullable();
            $table->unsignedBigInteger('province_id')->nullable();
            $table->unsignedBigInteger('city_id')->nullable();
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();

        });
    }

    public function down(): void
    {
        Schema::table('shops', function (Blueprint $table) {
            $table->dropColumn([
                'seller_type',
                'description',
                'phone',
                'email',
                'address',
                'province_id',
                'city_id',
                'latitude',
                'longitude',
            ]);
        });
    }
};
