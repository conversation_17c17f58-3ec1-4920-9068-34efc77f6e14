<?php

namespace App\Service\Actions\Shop;

use App\Models\User\Shop;

class StoreShopData
{
    /**
     * Persist a new shop row and return it.
     *
     * @param  array  $data   // validated payload from StoreShopRequest
     * @return Shop
     */
    public function handle(array $data): Shop
    {
        $shop = Shop::find($data['shop_id']);
        $shop->update($data);


        /** @var \Illuminate\Http\UploadedFile $file */
        $file = $data['image'];

        $date = now();
        $path = "{$date->year}/{$date->month}/{$date->day}";

        $path = $file->store($path, 'shops');

        $shop->gallery()->create([
            "image_path" => $path,
        ]);
        return $shop;
    }
}