<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for adding price_unit_id to wallet_transactions table.
 */
return new class extends Migration {
    public function up(): void
    {
        Schema::table('wallet_transactions', function (Blueprint $table) {
            $table->foreignId('price_unit_id')
                ->nullable()
                ->constrained('price_units')
                ->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::table('wallet_transactions', function (Blueprint $table) {
            $table->dropForeign(['price_unit_id']);
            $table->dropColumn('price_unit_id');
        });
    }
};
