<?php

namespace App\Models\Shopping;

use App\Traits\DeliveryMethod\DeliveryMethodRelationsTrait;
use Illuminate\Database\Eloquent\Model;

class DeliveryMethod extends Model
{
    use DeliveryMethodRelationsTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'price',
        'active',
        'shop_id', // If shop_id is null, the delivery method is global, otherwise it's specific to a shop
        'image_url', // URL to the delivery method's image (e.g., shipping company logo)
        'price_unit_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'price' => 'float',
        'active' => 'boolean',
        'shop_id' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

}
