<?php

namespace App\Http\Resources\Shopping;

use App\Http\Resources\Product\ProductDetailResource;
use App\Models\Product\ProductDetail;
use App\Models\Product\PriceUnit;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Product\ProductAttributesResource;

/**
 * Resource class for transforming InvoiceProduct models into API responses.
 *
 * Maintains consistency with CartItemResource structure for uniform product representation.
 */
class InvoiceProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'product_id' => $this->product_id,
            'id' => $this->product_variant_id,
            'name' => $this->name,
            'sku' => $this->sku,
            'price' => $this->price,
            'sale_price' => $this->sale_price,
            'price_unit' => $this->whenLoaded('priceUnit', fn() => $this->priceUnit?->name) ?? PriceUnit::getDefault()?->name,
            'quantity' => $this->quantity,
            'discount' => $this->discount,
            'total' => $this->total,
            'image' => $this->image,
            'shop_name' => $this->shop_name,
            'attributes' => $this->details ? $this->details->map(fn($detail) => [
                'title' => $detail->title,
                'value' => $detail->value,
            ])
                : [],
            'guarantee' => $this->guarantee_id ? [
                "months" => $this->guarantee_months,
                "company_name" => $this->guarantee_company_name,
                "price" => $this->guarantee_price,
            ] : null,
        ];
    }
}
